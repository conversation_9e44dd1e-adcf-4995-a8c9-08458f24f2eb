from schema_sdk.steeleye_model.gamma import <PERSON>Meta<PERSON>ields

from se_api_svc.repository.comms.utils.calendar_search_filter import ExternalFilter, InternalFilter

# flake8: noqa


def test_internal_filter():
    es_filter_clause = InternalFilter(param="internal", value=["steel-eye.com"]).to_dict(
        meta_fields=GammaMetaFields
    )
    assert es_filter_clause == {
        "script": {
            "script": {
                "lang": "painless",
                "params": {"domains": ["steel-eye.com"]},
                "inline": """
    def is_internal = [true];
    for (p in params._source.participants) {
        def key = p.value['&key'];                      
        if (key.contains('MarketPerson')) { is_internal.add(false); }
    }
    
    def emails = params._source.identifiers['allIds'];
    def email_internal_check = [true];
        
    for(email in emails){
        def email_domains = [false];        

        for(domain in params.domains){
            if(email.contains(domain)){
                email_domains.add(true);
            }
            else{
                email_domains.add(false);
            }
        }
        email_internal_check.add(email_domains.contains(true));
    }
    
    if(email_internal_check.contains(false)){
        is_internal.add(false);
    }

    return !is_internal.contains(false);
""",
            }
        }
    }


def test_external_filter():
    es_filter_clause = ExternalFilter(param="internal", value=["steel-eye.com"]).to_dict(
        meta_fields=GammaMetaFields
    )
    assert es_filter_clause == {
        "script": {
            "script": {
                "lang": "painless",
                "params": {"domains": ["steel-eye.com"]},
                "inline": """
    def is_external = [false];
    for (p in params._source.participants) {
        def key = p.value['&key'];                      
        if (key.contains('MarketPerson')) { is_external.add(true); }
    }
    
    def emails = params._source.identifiers['allIds'];
    def email_internal_check = [true];
        
    for(email in emails){
        def email_domains = [false];        

        for(domain in params.domains){
            if(email.contains(domain)){
                email_domains.add(true);
            }
            else{
                email_domains.add(false);
            }
        }
        email_internal_check.add(email_domains.contains(true));
    }
    
    if(email_internal_check.contains(false)){
        is_external.add(true);
    }

    return is_external.contains(true);
""",
            }
        }
    }
